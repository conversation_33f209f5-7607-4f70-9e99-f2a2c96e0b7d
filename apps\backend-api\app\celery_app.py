from celery import Celery
from celery.schedules import crontab
from app.config import settings

# Create Celery app
celery_app = Celery(
    "aggie",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    broker_connection_retry_on_startup=True,
    # Disable result backend to avoid serialization issues
    task_ignore_result=True,
    result_backend=None,
)

# Function to manually discover tasks when needed
def discover_tasks():
    """Manually discover and import task modules"""
    try:
        from app.tasks import erp_integration, notifications, invoice_processing_tasks
        return True
    except ImportError as e:
        print(f"Warning: Could not import some task modules: {e}")
        return False

# Scheduled tasks
celery_app.conf.beat_schedule = {
    # Future scheduled tasks can be added here
}

if __name__ == "__main__":
    celery_app.start()
